import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { flatColors as colors, spacing } from '../../theme';

interface Driver {
  id: string;
  name: string;
  rating: number;
  distance: number;
  location: {
    latitude: number;
    longitude: number;
  };
  vehicle: {
    make: string;
    model: string;
    plateNumber: string;
  };
}

interface NearbyDriversOverlayProps {
  drivers: Driver[];
}

export const NearbyDriversOverlay: React.FC<NearbyDriversOverlayProps> = ({
  drivers,
}) => {
  if (drivers.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Icon name="local-taxi" size={16} color={colors.primary} />
        <Text style={styles.headerText}>
          {drivers.length} driver{drivers.length !== 1 ? 's' : ''} nearby
        </Text>
        <TouchableOpacity style={styles.refreshButton}>
          <Icon name="refresh" size={16} color={colors.gray[600]} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 120,
    right: 20,
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    elevation: 5,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    maxWidth: 200,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginLeft: spacing.xs,
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    flex: 1,
  },
  refreshButton: {
    padding: spacing.xs,
  },
});
