import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { flatColors as colors, spacing } from '../../theme';

interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

interface LocationSuggestion {
  id: string;
  address: string;
  location: Location;
}

interface LocationInputProps {
  placeholder: string;
  value?: Location | null;
  onLocationSelect: (location: Location) => void;
  icon?: string;
}

export const LocationInput: React.FC<LocationInputProps> = ({
  placeholder,
  value,
  onLocationSelect,
  icon = 'location-on',
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);

  // Mock location suggestions for Jordan
  const mockSuggestions: LocationSuggestion[] = [
    {
      id: '1',
      address: 'Downtown Amman, Jordan',
      location: { latitude: 31.9539, longitude: 35.9106 },
    },
    {
      id: '2',
      address: 'Abdali Mall, Amman, Jordan',
      location: { latitude: 31.9634, longitude: 35.9015 },
    },
    {
      id: '3',
      address: 'University of Jordan, Amman',
      location: { latitude: 32.0167, longitude: 35.8725 },
    },
    {
      id: '4',
      address: 'Queen Alia International Airport',
      location: { latitude: 31.7226, longitude: 35.9932 },
    },
    {
      id: '5',
      address: 'Rainbow Street, Amman',
      location: { latitude: 31.9515, longitude: 35.9239 },
    },
    {
      id: '6',
      address: 'City Mall, Amman',
      location: { latitude: 31.9973, longitude: 35.8729 },
    },
    {
      id: '7',
      address: 'Mecca Mall, Amman',
      location: { latitude: 31.9045, longitude: 35.8662 },
    },
    {
      id: '8',
      address: 'Jordan Gate, Amman',
      location: { latitude: 31.9754, longitude: 35.8372 },
    },
  ];

  const handleSearch = (text: string) => {
    setSearchText(text);
    if (text.length > 2) {
      const filtered = mockSuggestions.filter(suggestion =>
        suggestion.address.toLowerCase().includes(text.toLowerCase())
      );
      setSuggestions(filtered);
    } else {
      setSuggestions(mockSuggestions);
    }
  };

  const handleLocationSelect = (location: LocationSuggestion) => {
    onLocationSelect({
      latitude: location.location.latitude,
      longitude: location.location.longitude,
      address: location.address,
    });
    setIsModalVisible(false);
    setSearchText('');
  };

  const openModal = () => {
    setIsModalVisible(true);
    setSuggestions(mockSuggestions);
  };

  const renderSuggestion = ({ item }: { item: LocationSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleLocationSelect(item)}
    >
      <Icon name="location-on" size={20} color={colors.gray[500]} />
      <View style={styles.suggestionText}>
        <Text style={styles.suggestionAddress}>{item.address}</Text>
      </View>
      <Icon name="arrow-forward-ios" size={16} color={colors.gray[400]} />
    </TouchableOpacity>
  );

  return (
    <>
      <TouchableOpacity style={styles.inputContainer} onPress={openModal}>
        <Icon name={icon} size={20} color={colors.primary} />
        <Text style={[styles.inputText, !value && styles.placeholderText]}>
          {value?.address || placeholder}
        </Text>
        <Icon name="search" size={20} color={colors.gray[400]} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setIsModalVisible(false)}
            >
              <Icon name="close" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Location</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.searchContainer}>
            <Icon name="search" size={20} color={colors.gray[500]} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for a location..."
              value={searchText}
              onChangeText={handleSearch}
              autoFocus
            />
            {searchText.length > 0 && (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Icon name="clear" size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            )}
          </View>

          <FlatList
            data={suggestions}
            renderItem={renderSuggestion}
            keyExtractor={(item) => item.id}
            style={styles.suggestionsList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[200],
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  inputText: {
    flex: 1,
    marginLeft: spacing.sm,
    fontSize: 16,
    color: colors.gray[900],
  },
  placeholderText: {
    color: colors.gray[500],
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  closeButton: {
    padding: spacing.xs,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
  },
  placeholder: {
    width: 32,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    margin: spacing.lg,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.sm,
    fontSize: 16,
    color: colors.gray[900],
  },
  suggestionsList: {
    flex: 1,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  suggestionText: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  suggestionAddress: {
    fontSize: 16,
    color: colors.gray[900],
  },
});
