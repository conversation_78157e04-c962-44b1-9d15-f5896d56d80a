import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { flatColors as colors, spacing } from '../../theme';
import { LocationInput } from './LocationInput';

const { width } = Dimensions.get('window');

interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

interface BookingPanelProps {
  pickupLocation: Location | null;
  destinationLocation: Location | null;
  onPickupChange: (location: Location) => void;
  onDestinationChange: (location: Location) => void;
  onBookRide: () => void;
  nearbyDriversCount: number;
}

export const BookingPanel: React.FC<BookingPanelProps> = ({
  pickupLocation,
  destinationLocation,
  onPickupChange,
  onDestinationChange,
  onBookRide,
  nearbyDriversCount,
}) => {
  const canBookRide = pickupLocation && destinationLocation;

  return (
    <View style={styles.container}>
      {/* Handle Bar */}
      <View style={styles.handleBar} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Book a Ride</Text>
        {nearbyDriversCount > 0 && (
          <View style={styles.driversInfo}>
            <Icon name="local-taxi" size={16} color={colors.primary} />
            <Text style={styles.driversText}>
              {nearbyDriversCount} driver{nearbyDriversCount !== 1 ? 's' : ''} nearby
            </Text>
          </View>
        )}
      </View>

      {/* Location Inputs */}
      <View style={styles.locationInputs}>
        <View style={styles.inputWrapper}>
          <LocationInput
            placeholder="Pickup location"
            value={pickupLocation}
            onLocationSelect={onPickupChange}
            icon="radio-button-checked"
          />
        </View>

        <View style={styles.inputSeparator}>
          <View style={styles.separatorLine} />
          <View style={styles.separatorDots}>
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
          </View>
        </View>

        <View style={styles.inputWrapper}>
          <LocationInput
            placeholder="Where to?"
            value={destinationLocation}
            onLocationSelect={onDestinationChange}
            icon="location-on"
          />
        </View>
      </View>

      {/* Service Type Selection */}
      <View style={styles.serviceTypes}>
        <TouchableOpacity style={[styles.serviceType, styles.serviceTypeActive]}>
          <Icon name="local-taxi" size={24} color={colors.primary} />
          <Text style={[styles.serviceTypeText, styles.serviceTypeTextActive]}>
            Standard
          </Text>
          <Text style={styles.serviceTypePrice}>JD 3.50</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.serviceType}>
          <Icon name="directions-car" size={24} color={colors.gray[500]} />
          <Text style={styles.serviceTypeText}>Premium</Text>
          <Text style={styles.serviceTypePrice}>JD 5.00</Text>
        </TouchableOpacity>
      </View>

      {/* Booking Button */}
      <TouchableOpacity
        style={[styles.bookButton, !canBookRide && styles.bookButtonDisabled]}
        onPress={onBookRide}
        disabled={!canBookRide}
      >
        <Text style={[styles.bookButtonText, !canBookRide && styles.bookButtonTextDisabled]}>
          {canBookRide ? 'Book Ride' : 'Select pickup and destination'}
        </Text>
        {canBookRide && (
          <Icon name="arrow-forward" size={20} color={colors.white} />
        )}
      </TouchableOpacity>

      {/* Estimated Time and Price */}
      {canBookRide && (
        <View style={styles.estimateInfo}>
          <View style={styles.estimateItem}>
            <Icon name="schedule" size={16} color={colors.gray[500]} />
            <Text style={styles.estimateText}>5-8 min</Text>
          </View>
          <View style={styles.estimateItem}>
            <Icon name="attach-money" size={16} color={colors.gray[500]} />
            <Text style={styles.estimateText}>JD 3.50 - 5.00</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    elevation: 10,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: colors.gray[300],
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: spacing.sm,
    marginBottom: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.gray[900],
  },
  driversInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '15',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  driversText: {
    marginLeft: spacing.xs,
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  locationInputs: {
    marginBottom: spacing.lg,
  },
  inputWrapper: {
    marginBottom: spacing.xs,
  },
  inputSeparator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xs,
    paddingLeft: spacing.lg,
  },
  separatorLine: {
    width: 2,
    height: 20,
    backgroundColor: colors.gray[300],
    marginRight: spacing.sm,
  },
  separatorDots: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  dot: {
    width: 3,
    height: 3,
    backgroundColor: colors.gray[400],
    borderRadius: 1.5,
    marginVertical: 1,
  },
  serviceTypes: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  serviceType: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
    backgroundColor: colors.white,
  },
  serviceTypeActive: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  serviceTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[600],
    marginTop: spacing.xs,
  },
  serviceTypeTextActive: {
    color: colors.primary,
  },
  serviceTypePrice: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 2,
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: spacing.md,
    marginBottom: spacing.sm,
  },
  bookButtonDisabled: {
    backgroundColor: colors.gray[300],
  },
  bookButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginRight: spacing.sm,
  },
  bookButtonTextDisabled: {
    color: colors.gray[500],
  },
  estimateInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.lg,
  },
  estimateItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  estimateText: {
    marginLeft: spacing.xs,
    fontSize: 14,
    color: colors.gray[600],
  },
});
